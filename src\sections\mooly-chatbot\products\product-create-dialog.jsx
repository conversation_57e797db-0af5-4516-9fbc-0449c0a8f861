'use client';

import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMemo, useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Snackbar from '@mui/material/Snackbar';
import CardContent from '@mui/material/CardContent';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';

import { checkSKUExists } from 'src/actions/mooly-chatbot/product-api';
import { useProductMutations } from 'src/actions/mooly-chatbot/product-service';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

// Import các component thông thường
import ProductSeo from './product-seo';
import ProductType from './product-type';
import ProductTags from './product-tags';
import ProductMedia from './product-media';
import ProductLabels from './product-labels';
import ProductPricing from './product-pricing';
import ProductVariants from './product-variants';
import ProductBasicInfo from './product-basic-info';
import { FormDebugPanel } from './components/form-debug-panel';
import ProductPricingAdvanced from './product-pricing-advanced';
import SimpleProductForm from './components/simple-product-form';
import { ProductSchema, SimpleProductSchema, defaultValues, simpleProductDefaultValues } from './product-schema';

// ----------------------------------------------------------------------

export default function ProductCreateDialog({ open, onClose, productId, currentProduct }) {
  // console.log(currentProduct);
  // Xác định chế độ: tạo mới hoặc sửa
  const isEditMode = !!currentProduct;
  const [isUploading] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // Sử dụng hook để tạo/cập nhật sản phẩm
  const { createProduct, updateProduct, isMutating, error } = useProductMutations();

  // Xử lý hiển thị thông báo lỗi
  useEffect(() => {
    if (error) {
      setSnackbar({
        open: true,
        message: `Lỗi: ${error.message || 'Đã xảy ra lỗi khi xử lý sản phẩm'}`,
        severity: 'error',
      });
    }
  }, [error]);

  /**
   * Chuyển đổi attributes từ object format sang array format
   * @param {Object|Array} attributes - Thuộc tính ở dạng object hoặc array
   * @returns {Array} - Thuộc tính ở dạng array
   */
  const normalizeAttributes = (attributes) => {
    // Nếu đã là array, trả về nguyên vẹn
    if (Array.isArray(attributes)) {
      return attributes;
    }

    // Nếu là object, chuyển đổi sang array format
    if (attributes && typeof attributes === 'object') {
      return Object.entries(attributes).map(([name, values]) => ({
        name,
        values: Array.isArray(values) ? values : [],
      }));
    }

    // Trường hợp khác, trả về array rỗng
    return [];
  };

  // Xử lý dữ liệu currentProduct an toàn để tránh lỗi undefined/null
  const safeCurrentProduct = useMemo(() => {
    if (!currentProduct) return null;

    const normalizedAttributes = normalizeAttributes(currentProduct.attributes);

    return {
      ...currentProduct,
      // Đảm bảo các field array luôn là array
      tags: Array.isArray(currentProduct.tags) ? currentProduct.tags : [],
      gender: Array.isArray(currentProduct.gender) ? currentProduct.gender : [],
      images: Array.isArray(currentProduct.images) ? currentProduct.images : [],
      variants: Array.isArray(currentProduct.variants) ? currentProduct.variants : [],
      // Đảm bảo các field object có giá trị mặc định
      saleLabel: currentProduct.saleLabel || { enabled: false, content: '' },
      newLabel: currentProduct.newLabel || { enabled: false, content: '' },
      // Normalize attributes từ object sang array format
      attributes: normalizedAttributes,
    };
  }, [currentProduct]);

  // Sử dụng mode 'onBlur' thay vì 'all' để giảm số lần validation
  const methods = useForm({
    mode: 'onBlur', // Chỉ validate khi blur, giảm số lần validation không cần thiết
    resolver: zodResolver(ProductSchema), // Sử dụng schema đầy đủ để tương thích
    defaultValues,
    values: safeCurrentProduct,
  });

  const {
    reset,
    watch,
    setValue,
    getValues,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  // Function để validate SKU trước khi submit
  const validateSKUBeforeSubmit = useCallback(async (sku, excludeId = null) => {
    if (!sku || sku.trim() === '') {
      throw new Error('SKU không được để trống');
    }

    try {
      const exists = await checkSKUExists(sku, excludeId);
      if (exists) {
        throw new Error('SKU đã tồn tại. Vui lòng sử dụng SKU khác.');
      }
    } catch (checkError) {
      // Nếu có lỗi khi kiểm tra SKU, log và ném lại lỗi
      console.error('Error checking SKU:', checkError);
      throw new Error('Không thể kiểm tra SKU. Vui lòng thử lại.');
    }
  }, []);

  // Sử dụng handleSubmit từ react-hook-form với submitHandler
  const onSubmit = handleSubmit(async () => {
    const data = getValues();
    const variants = getValues('variants') || [];

    try {
      // Validate SKU trước khi submit
      await validateSKUBeforeSubmit(data.sku, isEditMode ? productId : null);

      // Tối ưu dữ liệu cho sản phẩm simple
      const productData = { ...data };

      // Nếu là sản phẩm simple, loại bỏ các trường không cần thiết
      if (isSimpleProduct) {
        // Chỉ giữ lại các trường cần thiết cho sản phẩm simple
        const simpleProductData = {
          name: data.name,
          slug: data.slug || '',
          description: data.description || '',
          shortDescription: data.shortDescription || '',
          categoryId: data.categoryId,
          type: data.type,
          sku: data.sku,
          price: data.price,
          stockQuantity: data.stockQuantity || 0,
          trackInventory: data.trackInventory !== false,
          images: data.images || [],
          avatar: data.avatar || null,
          isActive: data.isActive !== false,
          // Các trường mặc định cho sản phẩm simple
          attributes: [],
          variants: [],
          tags: [],
          gender: [],
          saleLabel: { enabled: false, content: '' },
          newLabel: { enabled: false, content: '' },
          metaKeywords: [],
          isFeatured: false,
        };
        Object.assign(productData, simpleProductData);
      } else {
        productData.newVariants = variants;
      }

      let result;
      if (isEditMode) {
        result = await updateProduct(productId, productData, currentProduct);
      } else {
        result = await createProduct(productData);
      }

      if (result.success) {
        setSnackbar({
          open: true,
          message: isEditMode ? 'Cập nhật sản phẩm thành công!' : 'Tạo sản phẩm mới thành công!',
          severity: 'success',
        });

        // Đóng dialog sau khi thành công
        setTimeout(() => {
          if (!isEditMode) {
            reset();
          }
          onClose();
        }, 1000);
      } else {
        setSnackbar({
          open: true,
          message: `Lỗi: ${result.error?.message || (isEditMode ? 'Đã xảy ra lỗi khi cập nhật sản phẩm' : 'Đã xảy ra lỗi khi tạo sản phẩm mới')}`,
          severity: 'error',
        });
      }
    } catch (err) {
      // Handle SKU validation error và các lỗi khác
      setSnackbar({
        open: true,
        message: `Lỗi: ${err.message || 'Đã xảy ra lỗi không xác định'}`,
        severity: 'error',
      });
    }
  });

  // Xử lý hủy form - sử dụng useCallback để tránh tạo lại hàm
  const handleCancel = useCallback(() => {
    reset();
    onClose();
  }, [reset, onClose]);

  // Memoize các giá trị để tránh re-render không cần thiết
  const isLoading = useMemo(
    () => isSubmitting || isMutating || isUploading,
    [isSubmitting, isMutating, isUploading]
  );

  const dialogTitle = useMemo(
    () => (isEditMode ? 'Cập nhật sản phẩm' : 'Thêm sản phẩm mới'),
    [isEditMode]
  );

  const submitButtonText = useMemo(() => (isLoading ? 'Đang lưu...' : 'Lưu'), [isLoading]);

  // Watch product type để hiển thị các trường phù hợp
  const productType = watch('type');
  const isSimpleProduct = productType === 'simple';

  return (
    <>
      <FormDebugPanel methods={methods} open={showDebug} onClose={() => setShowDebug(false)} />

      {/* Thông báo kết quả */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      <Dialog
        maxWidth="xl"
        fullWidth
        open={open}
        onClose={onClose}
        sx={{
          '& .MuiDialog-paper': {
            maxHeight: 'calc(100vh - 64px)', // Adjust height to prevent outer scrollbar
            height: 'calc(100vh - 64px)',
            display: 'flex',
            flexDirection: 'column',
          },
        }}
      >
        <Form methods={methods} onSubmit={onSubmit} noValidate autoComplete="off">
          <AppBar position="sticky" color="inherit" sx={{ boxShadow: 1 }}>
            <Toolbar>
              <IconButton edge="start" color="inherit" onClick={handleCancel}>
                <Iconify icon="eva:close-fill" />
              </IconButton>

              <Typography variant="h6" component="div" sx={{ flexGrow: 1, ml: 2 }}>
                {dialogTitle}
              </Typography>

              <IconButton color="primary" onClick={() => setShowDebug(!showDebug)}>
                <Iconify icon="eva:code-fill" />
              </IconButton>
            </Toolbar>
          </AppBar>

          <Box
            sx={{
              p: 3,
              flexGrow: 1,
              overflowY: 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(0,0,0,0.2)',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'transparent',
              },
            }}
          >
            <Box sx={{ maxWidth: 960, mx: 'auto' }}>
              {isSimpleProduct ? (
                // Form đơn giản cho sản phẩm simple
                <SimpleProductForm watch={watch} setValue={setValue} />
              ) : (
                // Form đầy đủ cho các loại sản phẩm khác
                <Stack spacing={3}>
                  {/* Loại sản phẩm */}
                  <Card sx={{ mb: 2, border: 2, borderColor: 'primary.main', boxShadow: 2 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                        Loại sản phẩm
                        <Typography component="span" variant="caption" sx={{ ml: 1, color: 'primary.main' }}>
                          (Quan trọng)
                        </Typography>
                      </Typography>
                      <ProductType />
                    </CardContent>
                  </Card>

                  {/* Thông tin cơ bản */}
                  <Card sx={{ mb: 2, border: 2, borderColor: 'primary.main', boxShadow: 2 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                        Thông tin cơ bản
                        <Typography component="span" variant="caption" sx={{ ml: 1, color: 'primary.main' }}>
                          (Quan trọng)
                        </Typography>
                      </Typography>
                      <ProductBasicInfo />
                    </CardContent>
                  </Card>

                  {/* Hình ảnh và video */}
                  <Card sx={{ mb: 2, border: 2, borderColor: 'primary.main', boxShadow: 2 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                        Hình ảnh và video
                        <Typography component="span" variant="caption" sx={{ ml: 1, color: 'primary.main' }}>
                          (Quan trọng)
                        </Typography>
                      </Typography>
                      <ProductMedia watch={watch} setValue={setValue} />
                    </CardContent>
                  </Card>

                  {/* Biến thể sản phẩm */}
                  <Card sx={{ mb: 2, border: 1, borderColor: 'divider', boxShadow: 1 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2, color: 'text.primary', fontWeight: 500 }}>
                        Biến thể sản phẩm
                      </Typography>
                      <ProductVariants
                        watch={watch}
                        setValue={setValue}
                        getValues={getValues}
                        isEditMode={isEditMode}
                      />
                    </CardContent>
                  </Card>

                  {/* Giá và tồn kho */}
                  <Card sx={{ mb: 2, border: 2, borderColor: 'primary.main', boxShadow: 2 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                        Giá và tồn kho
                        <Typography component="span" variant="caption" sx={{ ml: 1, color: 'primary.main' }}>
                          (Quan trọng)
                        </Typography>
                      </Typography>
                      <ProductPricing watch={watch} setValue={setValue} />
                    </CardContent>
                  </Card>

                  {/* Giá nâng cao và thuế */}
                  <Card sx={{ mb: 2, border: 1, borderColor: 'divider', boxShadow: 1 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2, color: 'text.primary', fontWeight: 500 }}>
                        Giá nâng cao và thuế
                      </Typography>
                      <ProductPricingAdvanced />
                    </CardContent>
                  </Card>

                  {/* Tags và thuộc tính */}
                  <Card sx={{ mb: 2, border: 1, borderColor: 'divider', boxShadow: 1 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2, color: 'text.primary', fontWeight: 500 }}>
                        Tags và thuộc tính
                      </Typography>
                      <ProductTags />
                    </CardContent>
                  </Card>

                  {/* Labels và trạng thái */}
                  <Card sx={{ mb: 2, border: 1, borderColor: 'divider', boxShadow: 1 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2, color: 'text.primary', fontWeight: 500 }}>
                        Labels và trạng thái
                      </Typography>
                      <ProductLabels />
                    </CardContent>
                  </Card>

                  {/* SEO */}
                  <Card sx={{ mb: 2, border: 1, borderColor: 'divider', boxShadow: 1 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2, color: 'text.primary', fontWeight: 500 }}>
                        Tối ưu SEO
                      </Typography>
                      <ProductSeo watch={watch} setValue={setValue} />
                    </CardContent>
                  </Card>
                </Stack>
              )}
            </Box>
          </Box>

          <Box
            sx={{
              position: 'sticky',
              bottom: 0,
              left: 0,
              right: 0,
              zIndex: 1000,
              p: 3,
              borderTop: 1,
              borderColor: 'divider',
              backgroundColor: 'background.paper',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              boxShadow: 2,
            }}
          >
            <Field.Switch name="isActive" label="Kích hoạt" />

            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                color="inherit"
                variant="outlined"
                onClick={handleCancel}
                disabled={isLoading}
              >
                Hủy
              </Button>

              <Button
                type="submit"
                variant="contained"
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {submitButtonText}
              </Button>
            </Box>
          </Box>
        </Form>
      </Dialog>
    </>
  );
}

ProductCreateDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  storeId: PropTypes.string,
  productId: PropTypes.string,
  currentProduct: PropTypes.object,
};
