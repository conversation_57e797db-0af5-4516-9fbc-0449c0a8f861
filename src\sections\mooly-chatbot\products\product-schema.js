import { z as zod } from 'zod';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';

import { schemaHelper } from 'src/components/hook-form';

// ----------------------------------------------------------------------

// Schema đơn giản cho sản phẩm simple
export const SimpleProductSchema = zod.object({
  // Thông tin cơ bản - chỉ cần thiết cho simple product
  name: zod.string().min(1, 'Tên sản phẩm là bắt buộc'),
  description: schemaHelper.editor().optional(),

  // Loại sản phẩm - cố định là simple
  type: zod.literal(PRODUCT_TYPES.SIMPLE),

  // Phân loại - tùy chọn cho simple product
  categoryId: zod.string().optional(),

  // Mã sản phẩm - tự động tạo nếu không có
  sku: zod.string().optional(),

  // Giá - bắt buộc cho simple product
  price: zod.number({ coerce: true }).min(0, 'Giá phải lớn hơn 0'),

  // Hình ảnh - bắt buộc ít nhất 1 ảnh
  images: zod.array(zod.any()).min(1, 'Ít nhất một hình ảnh sản phẩm là bắt buộc').default([]),
  avatar: zod.any().optional(),

  // Trạng thái
  isActive: zod.boolean().default(true),
});

// Schema đầy đủ cho các loại sản phẩm khác
export const ProductSchema = zod.object({
  // Thông tin cơ bản
  name: zod.string().min(1, 'Tên sản phẩm là bắt buộc'),
  slug: zod.string().optional(),
  description: schemaHelper.editor().optional(),
  shortDescription: zod.string().optional(),

  // Phân loại
  categoryId: zod.string().min(1, 'Danh mục là bắt buộc'),
  type: zod
    .enum([
      PRODUCT_TYPES.SIMPLE,
      PRODUCT_TYPES.VARIABLE,
      PRODUCT_TYPES.DIGITAL,
      PRODUCT_TYPES.SERVICE,
    ])
    .default(PRODUCT_TYPES.VARIABLE),

  // Mã sản phẩm
  sku: zod.string().min(1, 'Mã SKU là bắt buộc'),

  // Giá và tồn kho
  price: zod.union([zod.number({ coerce: true }).min(0, 'Giá phải lớn hơn 0'), zod.null()]),
  costPrice: zod.union([zod.number({ coerce: true }).min(0, 'Giá vốn phải lớn hơn 0'), zod.null()]).optional(),
  salePrice: zod.union([zod.number({ coerce: true }).min(0, 'Giá khuyến mãi phải lớn hơn 0'), zod.null()]).optional(),
  stockQuantity: zod.union([zod.number({ coerce: true }).min(0, 'Số lượng tồn kho phải lớn hơn 0'), zod.null()]),
  trackInventory: zod.boolean().default(true),

  // Kích thước và trọng lượng
  weight: zod.union([zod.number({ coerce: true }).min(0, 'Trọng lượng phải lớn hơn 0'), zod.null()]).optional(),
  length: zod.union([zod.number({ coerce: true }).min(0, 'Chiều dài phải lớn hơn 0'), zod.null()]).optional(),
  width: zod.union([zod.number({ coerce: true }).min(0, 'Chiều rộng phải lớn hơn 0'), zod.null()]).optional(),
  height: zod.union([zod.number({ coerce: true }).min(0, 'Chiều cao phải lớn hơn 0'), zod.null()]).optional(),

  // Hình ảnh và media
  images: zod.array(zod.any())
    .min(1, 'Ít nhất một hình ảnh sản phẩm là bắt buộc')
    .default([]),
  avatar: zod.any().optional(),

  // Biến thể và thuộc tính
  attributes: zod.array(zod.object({
    name: zod.string(),
    values: zod.array(zod.string())
  })).default([]),
  variants: zod.any().default([]),
  hasVariants: zod.boolean().default(false),

  // Tags và labels
  tags: zod.array(zod.string()).default([]),
  gender: zod.array(zod.string()).default([]),

  // Labels sản phẩm
  saleLabel: zod.object({
    enabled: zod.boolean().default(false),
    content: zod.string().default(''),
  }).default({ enabled: false, content: '' }),

  newLabel: zod.object({
    enabled: zod.boolean().default(false),
    content: zod.string().default(''),
  }).default({ enabled: false, content: '' }),

  // Thuế và pricing
  taxes: zod.union([zod.number({ coerce: true }).min(0, 'Thuế phải lớn hơn 0'), zod.null()]).optional(),
  includeTaxes: zod.boolean().default(false),

  // SEO
  metaKeywords: zod.array(zod.string()).default([]),
  seoTitle: zod.string().optional(),
  seoDescription: zod.string().optional(),

  // Trạng thái
  isActive: zod.boolean().default(true),
  isFeatured: zod.boolean().default(false),

  // Thông tin bổ sung từ database
  marketingInfo: zod.any().default({}),
  inventorySettings: zod.any().default({}),
  pricingSettings: zod.any().default({}),
  digitalProductInfo: zod.any().default({}),
  serviceInfo: zod.any().default({}),
  bundleInfo: zod.any().default({}),
  dimensions: zod.any().default({}),
});

// ----------------------------------------------------------------------

// Default values cho sản phẩm simple
export const simpleProductDefaultValues = {
  name: '',
  description: '',
  type: PRODUCT_TYPES.SIMPLE,
  categoryId: '',
  sku: '',
  price: 0,
  images: [],
  avatar: null,
  isActive: true,
};

// Default values đầy đủ cho các loại sản phẩm khác
export const defaultValues = {
  // Thông tin cơ bản
  name: '',
  slug: '',
  description: '',
  shortDescription: '',

  // Phân loại
  categoryId: '',
  type: PRODUCT_TYPES.VARIABLE,

  // Mã sản phẩm
  sku: '',

  // Giá và tồn kho
  price: null,
  costPrice: null,
  salePrice: null,
  stockQuantity: 0,
  trackInventory: true,

  // Kích thước và trọng lượng
  weight: null,
  length: null,
  width: null,
  height: null,

  // Hình ảnh và media
  images: [],
  avatar: null,

  // Biến thể và thuộc tính
  attributes: [],
  variants: [],

  // Tags và labels
  tags: [],
  gender: [],

  // Labels sản phẩm
  saleLabel: { enabled: false, content: '' },
  newLabel: { enabled: false, content: '' },

  // Thuế và pricing
  taxes: null,
  includeTaxes: false,

  // SEO
  metaKeywords: [],
  seoTitle: '',
  seoDescription: '',

  // Trạng thái
  isActive: true,
  isFeatured: false,

  // Thông tin bổ sung
  marketingInfo: {},
  inventorySettings: {},
  pricingSettings: {},
  digitalProductInfo: {},
  serviceInfo: {},
  bundleInfo: {},
  dimensions: {},
};
