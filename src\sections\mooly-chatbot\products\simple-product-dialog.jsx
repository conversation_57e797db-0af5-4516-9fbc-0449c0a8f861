'use client';

import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMemo, useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Snackbar from '@mui/material/Snackbar';
import CardContent from '@mui/material/CardContent';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';

import { useProductMutations } from 'src/actions/mooly-chatbot/product-service';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

import ProductMedia from './product-media';
import { SimpleProductSchema, simpleProductDefaultValues } from './product-schema';

// ----------------------------------------------------------------------

export default function SimpleProductDialog({ open, onClose, productId, currentProduct }) {
  const isEditMode = !!currentProduct;
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // Sử dụng hook để tạo/cập nhật sản phẩm
  const { createProduct, updateProduct, isMutating, error } = useProductMutations();

  // Xử lý hiển thị thông báo lỗi
  useEffect(() => {
    if (error) {
      setSnackbar({
        open: true,
        message: `Lỗi: ${error.message || 'Đã xảy ra lỗi khi xử lý sản phẩm'}`,
        severity: 'error',
      });
    }
  }, [error]);

  // Xử lý dữ liệu currentProduct cho simple product
  const safeCurrentProduct = useMemo(() => {
    if (!currentProduct) return null;

    return {
      name: currentProduct.name || '',
      description: currentProduct.description || '',
      type: 'simple',
      categoryId: currentProduct.categoryId || '',
      sku: currentProduct.sku || '',
      price: currentProduct.price || 0,
      images: Array.isArray(currentProduct.images) ? currentProduct.images : [],
      avatar: currentProduct.avatar || null,
      isActive: currentProduct.isActive !== undefined ? currentProduct.isActive : true,
    };
  }, [currentProduct]);

  const methods = useForm({
    mode: 'onBlur',
    resolver: zodResolver(SimpleProductSchema),
    defaultValues: simpleProductDefaultValues,
    values: safeCurrentProduct,
  });

  const {
    reset,
    watch,
    setValue,
    getValues,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  // Auto-generate SKU từ tên sản phẩm
  const watchedName = watch('name');
  useEffect(() => {
    if (watchedName && !isEditMode) {
      const autoSku = watchedName
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .substring(0, 20);
      setValue('sku', autoSku);
    }
  }, [watchedName, setValue, isEditMode]);

  const onSubmit = handleSubmit(async () => {
    const data = getValues();

    try {
      // Tự động tạo SKU nếu không có
      if (!data.sku) {
        const autoSku = `${data.name.toLowerCase().replace(/[^a-z0-9]/g, '').substring(0, 10)}-${Date.now()}`;
        data.sku = autoSku;
      }

      // Đảm bảo có categoryId mặc định
      if (!data.categoryId) {
        data.categoryId = null;
      }

      // Thêm các trường bắt buộc cho database
      const productData = {
        ...data,
        slug: data.name.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-'),
        stockQuantity: 0,
        trackInventory: false,
        shortDescription: '',
        // Các trường JSON mặc định
        attributes: {},
        marketingInfo: {},
        inventorySettings: {},
        pricingSettings: {},
        digitalProductInfo: {},
        serviceInfo: {},
        bundleInfo: {},
        dimensions: {},
      };

      let result;
      if (isEditMode) {
        result = await updateProduct(productId, productData, currentProduct);
      } else {
        result = await createProduct(productData);
      }

      if (result.success) {
        setSnackbar({
          open: true,
          message: isEditMode ? 'Cập nhật sản phẩm thành công!' : 'Tạo sản phẩm mới thành công!',
          severity: 'success',
        });

        setTimeout(() => {
          if (!isEditMode) {
            reset();
          }
          onClose();
        }, 1000);
      } else {
        setSnackbar({
          open: true,
          message: `Lỗi: ${result.error?.message || (isEditMode ? 'Đã xảy ra lỗi khi cập nhật sản phẩm' : 'Đã xảy ra lỗi khi tạo sản phẩm mới')}`,
          severity: 'error',
        });
      }
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Lỗi: ${err.message || 'Đã xảy ra lỗi không xác định'}`,
        severity: 'error',
      });
    }
  });

  const handleCancel = useCallback(() => {
    reset();
    onClose();
  }, [reset, onClose]);

  const isLoading = useMemo(
    () => isSubmitting || isMutating,
    [isSubmitting, isMutating]
  );

  const dialogTitle = useMemo(
    () => (isEditMode ? 'Cập nhật sản phẩm đơn giản' : 'Thêm sản phẩm đơn giản'),
    [isEditMode]
  );

  const submitButtonText = useMemo(() => (isLoading ? 'Đang lưu...' : 'Lưu'), [isLoading]);

  return (
    <>
      {/* Thông báo kết quả */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar((prev) => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      <Dialog
        maxWidth="md"
        fullWidth
        open={open}
        onClose={onClose}
        sx={{
          '& .MuiDialog-paper': {
            maxHeight: 'calc(100vh - 64px)',
            height: 'auto',
            display: 'flex',
            flexDirection: 'column',
          },
        }}
      >
        <Form methods={methods} onSubmit={onSubmit} noValidate autoComplete="off">
          <AppBar position="sticky" color="inherit" sx={{ boxShadow: 1 }}>
            <Toolbar>
              <IconButton edge="start" color="inherit" onClick={handleCancel}>
                <Iconify icon="eva:close-fill" />
              </IconButton>

              <Typography variant="h6" component="div" sx={{ flexGrow: 1, ml: 2 }}>
                {dialogTitle}
              </Typography>
            </Toolbar>
          </AppBar>

          <Box sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
            <Stack spacing={3} sx={{ maxWidth: 800, mx: 'auto' }}>
              {/* Thông tin cơ bản */}
              <Card sx={{ border: 2, borderColor: 'primary.main', boxShadow: 2 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                    Thông tin cơ bản
                  </Typography>
                  <Stack spacing={2}>
                    <Field.Text
                      name="name"
                      label="Tên sản phẩm"
                      required
                      placeholder="Nhập tên sản phẩm"
                    />
                    
                    <Field.Text
                      name="sku"
                      label="Mã SKU"
                      placeholder="Tự động tạo từ tên sản phẩm"
                      helperText="Để trống để tự động tạo từ tên sản phẩm"
                    />

                    <Field.Text
                      name="price"
                      label="Giá bán"
                      type="number"
                      required
                      placeholder="0"
                      InputProps={{
                        endAdornment: 'VNĐ',
                      }}
                    />

                    <Field.Editor
                      name="description"
                      label="Mô tả sản phẩm"
                      placeholder="Nhập mô tả chi tiết về sản phẩm..."
                    />
                  </Stack>
                </CardContent>
              </Card>

              {/* Hình ảnh */}
              <Card sx={{ border: 2, borderColor: 'primary.main', boxShadow: 2 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 600 }}>
                    Hình ảnh sản phẩm
                  </Typography>
                  <ProductMedia watch={watch} setValue={setValue} />
                </CardContent>
              </Card>
            </Stack>
          </Box>

          {/* Actions */}
          <Box
            sx={{
              position: 'sticky',
              bottom: 0,
              left: 0,
              right: 0,
              zIndex: 1000,
              p: 3,
              borderTop: 1,
              borderColor: 'divider',
              backgroundColor: 'background.paper',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              boxShadow: 2,
            }}
          >
            <Field.Switch name="isActive" label="Kích hoạt" />

            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                color="inherit"
                variant="outlined"
                onClick={handleCancel}
                disabled={isLoading}
              >
                Hủy
              </Button>

              <Button
                type="submit"
                variant="contained"
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {submitButtonText}
              </Button>
            </Box>
          </Box>
        </Form>
      </Dialog>
    </>
  );
}

SimpleProductDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  productId: PropTypes.string,
  currentProduct: PropTypes.object,
};
